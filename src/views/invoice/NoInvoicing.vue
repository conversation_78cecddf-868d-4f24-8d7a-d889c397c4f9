<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getNoInvoicingList, noInvoicingOnIssuance } from "../../services/invoice";
import type {
  NoInvoicingItem,
  NoInvoicingSearchParams,
} from "../../types/invoice";
import { useToast } from "primevue/usetoast";
import { formatChargeMonth, isoFormatYYmm } from "../../utils/common";

const toast = useToast();
const loading = ref(false);
const items = ref<NoInvoicingItem[]>([]);
const totalRecords = ref(0);
const selectedItems = ref<NoInvoicingItem[]>([]);

// 筛选表单
const searchForm = ref<NoInvoicingSearchParams>({
  account_seq: "",
  charge_month: undefined,
  customer_name: "",
  sub_order_no: "",
  page: 1,
  pageSize: 20,
});

// 日历控件的日期对象
const chargeMonthDate = ref<Date | null>(null);

// 退回操作相关状态
const onIssuanceDialogVisible = ref(false);
const onIssuanceLoading = ref(false);

// 处理权责账期日期变化
const onChargeMonthChange = (date: Date | null) => {
  chargeMonthDate.value = date;
  if (date) {
    searchForm.value.charge_month = parseInt(isoFormatYYmm(date) || "0");
  } else {
    searchForm.value.charge_month = undefined;
  }
};

// 获取数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm.value,
      page: searchForm.value.page || 1,
      pageSize: searchForm.value.pageSize || 20,
    };

    // 移除空值参数
    Object.keys(params).forEach((key) => {
      const paramKey = key as keyof typeof params;
      if (params[paramKey] === "" || params[paramKey] === undefined) {
        delete (params as any)[paramKey];
      }
    });

    const response = await getNoInvoicingList(params);
    if (response.code === 200) {
      items.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "获取数据失败",
        life: 3000,
      });
    }
  } catch (error) {
    console.error("获取不开票数据失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 分页处理
const onPage = (event: any) => {
  searchForm.value.page = event.page + 1;
  searchForm.value.pageSize = event.rows;
  loadData();
};

// 搜索
const handleSearch = () => {
  searchForm.value.page = 1;
  loadData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    account_seq: "",
    charge_month: undefined,
    customer_name: "",
    sub_order_no: "",
    page: 1,
    pageSize: 20,
  };
  chargeMonthDate.value = null;
  loadData();
};

// 加载客户选项
// Already handled by useCustomerOptions composable

// 打开退回操作确认对话框
const openOnIssuanceDialog = () => {
  if (selectedItems.value.length === 0) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择要退回的记录",
      life: 3000,
    });
    return;
  }
  onIssuanceDialogVisible.value = true;
};

// 关闭退回操作确认对话框
const closeOnIssuanceDialog = () => {
  onIssuanceDialogVisible.value = false;
};

// 执行退回操作
const executeOnIssuance = async () => {
  onIssuanceLoading.value = true;
  try {
    const requestData = {
      charge_ids: selectedItems.value.map((item) => item.id),
    };

    const response = await noInvoicingOnIssuance(requestData);
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: "退回操作成功",
        life: 3000,
      });
      closeOnIssuanceDialog();
      selectedItems.value = [];
      loadData();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: (response as any).msg || "退回操作失败",
        life: 3000,
      });
    }
  } catch (error: any) {
    console.error("退回操作失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "退回操作失败",
      life: 3000,
    });
  } finally {
    onIssuanceLoading.value = false;
  }
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <!-- 搜索工具栏 -->
  <Toolbar class="mb-2">
    <template #start>
      <div class="flex items-center">
        <Message severity="info">待开票-不开票</Message>
      </div>
    </template>
    <template #end>
      <div class="flex flex-wrap gap-2 items-center">
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">分账序号</label>
          <InputText v-model="searchForm.account_seq" class="w-48" />
        </FloatLabel>
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1"
            >子订单编号</label
          >
          <InputText v-model="searchForm.sub_order_no" class="w-48" />
        </FloatLabel>
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">客户名称</label>
          <InputText v-model="searchForm.customer_name" class="w-48" />
        </FloatLabel>
        <FloatLabel>
          <label class="text-sm font-medium text-gray-700 mb-1">权责账期</label>
          <DatePicker
            v-model="chargeMonthDate"
            view="month"
            dateFormat="yy-mm"
            showIcon
            class="w-60"
            @date-select="onChargeMonthChange"
          />
        </FloatLabel>
        <Button icon="pi pi-search" @click="handleSearch" rounded />
        <Button
          icon="pi pi-refresh"
          @click="handleReset"
          class="p-button-secondary"
          rounded
        />
      </div>
      <Divider layout="vertical" />
      <Button
        icon="pi pi-reply"
        label="退回"
        @click="openOnIssuanceDialog"
        :disabled="selectedItems.length === 0"
        severity="success"
      />
    </template>
  </Toolbar>

  <!-- 数据表格 -->
  <DataTable
    :value="items"
    v-model:selection="selectedItems"
    :lazy="true"
    :paginator="true"
    :rows="searchForm.pageSize"
    :rowsPerPageOptions="[10, 20, 50]"
    :totalRecords="totalRecords"
    :loading="loading"
    @page="onPage($event)"
    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
    currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
    showGridlines
    stripedRows
    scrollable
    scrollHeight="calc(100vh - 16rem)"
    class="rounded-lg shadow-sm p-datatable-sm"
    dataKey="id"
    resizableColumns
    columnResizeMode="fit"
  >
    <template #empty>
      <div class="empty-message">
        <i
          class="pi pi-inbox"
          style="
            font-size: 2rem;
            color: var(--p-text-color-secondary);
            margin-bottom: 1rem;
          "
        ></i>
        <p>暂无不开票数据</p>
      </div>
    </template>

    <Column selectionMode="multiple" headerStyle="width: 3rem"></Column>

    <Column field="sub_order_no" header="订单编号" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.sub_order_no }}</span>
      </template>
    </Column>

    <Column field="account_seq" header="分账序号" style="min-width: 10rem">
      <template #body="{ data }">
        <span class="font-mono text-sm">{{ data.account_seq }}</span>
      </template>
    </Column>

    <Column field="charge_month" header="权责账期" style="min-width: 8rem">
      <template #body="{ data }">
        <span>{{ formatChargeMonth(data.charge_month) }}</span>
      </template>
    </Column>

    <Column field="fee_amount" header="费用金额" style="min-width: 8rem">
      <template #body="{ data }">
        <span class="font-semibold text-green-600">¥{{ data.fee_amount }}</span>
      </template>
    </Column>

    <Column field="tax" header="税率" style="min-width: 6rem">
      <template #body="{ data }">
        <span>{{ data.tax }}%</span>
      </template>
    </Column>

    <Column field="tax_type" header="税率类型" style="min-width: 8rem">
      <template #body="{ data }">
        <Tag :value="data.tax_type" class="p-tag-info" />
      </template>
    </Column>

    <Column field="income_type" header="收入分类" style="min-width: 8rem">
      <template #body="{ data }">
        <Tag :value="data.income_type" class="p-tag-secondary" />
      </template>
    </Column>

    <Column field="pay_type" header="付费类型" style="min-width: 6rem">
      <template #body="{ data }">
        <Tag
          :value="data.pay_type"
          :class="data.pay_type === '预付' ? 'p-tag-success' : 'p-tag-warn'"
        />
      </template>
    </Column>

    <Column field="customer_name" header="客户名称" style="min-width: 12rem">
      <template #body="{ data }">
        <span class="font-medium">{{ data.customer_name }}</span>
      </template>
    </Column>

    <Column field="adjust_month" header="调账月份" style="min-width: 8rem">
      <template #body="{ data }">
        <span v-if="data.adjust_month">{{
          formatChargeMonth(data.adjust_month)
        }}</span>
        <span v-else class="text-gray-400">-</span>
      </template>
    </Column>
  </DataTable>

  <!-- 退回操作确认对话框 -->
  <Dialog
    v-model:visible="onIssuanceDialogVisible"
    modal
    header="确认退回"
    :style="{ width: '400px' }"
    @hide="closeOnIssuanceDialog"
    :draggable="false"
    :resizable="false"
  >
    <div class="confirmation-content">
      <div class="flex items-center gap-3 mb-4">
        <i class="pi pi-exclamation-triangle text-orange-500"></i>
        <span>确认要将选中的记录退回到待开票状态吗？</span>
      </div>
      <div class="text-gray-600 ml-7">
        已选择 <strong>{{ selectedItems.length }}</strong> 条记录
      </div>
    </div>
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="取消"
          icon="pi pi-times"
          @click="closeOnIssuanceDialog"
          class="p-button-secondary"
        />
        <Button
          label="确认退回"
          icon="pi pi-reply"
          @click="executeOnIssuance"
          :loading="onIssuanceLoading"
          severity="success"
        />
      </div>
    </template>
  </Dialog>

  <Toast />
</template>

<style scoped>
.empty-message {
  text-align: center;
  padding: 2rem;
  color: var(--p-text-color-secondary);
}

.p-toolbar {
  background: var(--p-surface-0);
  border: 1px solid var(--p-surface-border);
  border-radius: 0.5rem;
}

.p-datatable {
  border-radius: 0.5rem;
  overflow: hidden;
}

.p-datatable .p-datatable-header {
  background: var(--p-surface-50);
  border-bottom: 1px solid var(--p-surface-border);
}

.p-datatable .p-datatable-tbody > tr:hover {
  background: var(--p-surface-50);
}

.p-tag {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.p-tag-info {
  background: var(--p-blue-100);
  color: var(--p-blue-800);
}

.p-tag-secondary {
  background: var(--p-surface-100);
  color: var(--p-surface-700);
}

.p-tag-success {
  background: var(--p-green-100);
  color: var(--p-green-800);
}

.p-tag-warn {
  background: var(--p-orange-100);
  color: var(--p-orange-800);
}

/* 确认对话框样式 */
.confirmation-content {
  padding: 1rem 0;
}
</style>
