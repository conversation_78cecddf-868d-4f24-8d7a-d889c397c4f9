<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import { getFeeTemplates } from "../../services/feeTemplate";
import type {
  FeeTemplateItem,
  FeeTemplateParams,
} from "../../types/feeTemplate";
import {
  feeTemplateStateSeverityMap,
  feeTemplateStateValueMap,
  isLevelFeeMap,
  ownBusinessMap,
} from "../../types/feeTemplate";
import { formatDateTime } from "../../utils/common";

const feeTemplates = ref<FeeTemplateItem[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选相关
const selectedFilterColumn = ref("--");
const filterColumnOptions = [
  { label: "--", value: "--" },
  { label: "费用模板名称", value: "fee_template_name" },
  { label: "费用模板编码", value: "fee_template_code" },
  { label: "计费规则", value: "bill_rule" },
];
const filterValue = ref("");

// 加载费用模板列表数据
const loadFeeTemplates = async () => {
  try {
    loading.value = true;
    const params: FeeTemplateParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (selectedFilterColumn.value !== "--" && filterValue.value) {
      params.filter = filterValue.value;
      params.filterColumn = selectedFilterColumn.value;
    }

    const response = await getFeeTemplates(params);
    feeTemplates.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载费用模板列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadFeeTemplates();
};

// 处理搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadFeeTemplates();
};

// 重置搜索
const resetSearch = () => {
  selectedFilterColumn.value = "--";
  filterValue.value = "";
  lazyParams.value.page = 1;
  loadFeeTemplates();
};

onMounted(() => {
  loadFeeTemplates();
});
</script>

<template>
  <div class="fee-template-container">
    <Toast />
    <div class="card">
      <!-- 筛选工具栏 -->
      <Toolbar class="mb-3">
        <template #end>
          <div class="flex gap-2 flex-1 mr-3">
            <FloatLabel>
              <Select
                v-model="selectedFilterColumn"
                :options="filterColumnOptions"
                optionLabel="label"
                optionValue="value"
                style="min-width: 200px"
              />
            </FloatLabel>
            <FloatLabel>
              <InputText
                v-model="filterValue"
                style="min-width: 200px"
                @keyup.enter="handleSearch"
              />
              <label>搜索值</label>
            </FloatLabel>
          </div>
          <div class="flex gap-2">
            <Button icon="pi pi-search" rounded @click="handleSearch" />
            <Button
              icon="pi pi-refresh"
              severity="secondary"
              outlined
              rounded
              @click="resetSearch"
            />
          </div>
        </template>
      </Toolbar>

      <!-- 数据表格 -->
      <DataTable
        :value="feeTemplates"
        :paginator="true"
        :lazy="true"
        :loading="loading"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        @page="onPage($event)"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
        currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
        showGridlines
        stripedRows
        scrollable
        scrollHeight="calc(100vh - 22rem)"
        class="apple-datatable"
        resizableColumns
        columnResizeMode="fit"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无费用模板数据</p>
          </div>
        </template>

        <Column
          field="fee_template_name"
          header="模板名称"
          style="min-width: 15rem"
        >
          <template #body="slotProps">
            <div class="template-name">
              <i class="pi pi-tag template-icon"></i>
              {{ slotProps.data.fee_template_name }}
            </div>
          </template>
        </Column>

        <Column
          field="fee_template_code"
          header="模板编码"
          style="min-width: 10rem"
        >
          <template #body="slotProps">
            <Tag severity="info" :value="slotProps.data.fee_template_code" />
          </template>
        </Column>

        <Column field="bill_rule" header="计费规则" style="min-width: 12rem">
          <template #body="slotProps">
            <div class="bill-rule">
              {{ slotProps.data.bill_rule }}
            </div>
          </template>
        </Column>

        <Column field="own_business" header="归属业务" style="min-width: 8rem">
          <template #body="slotProps">
            <Tag
              :severity="slotProps.data.own_business === 0 ? 'info' : 'warning'"
              :value="ownBusinessMap[slotProps.data.own_business]"
            />
          </template>
        </Column>

        <Column field="is_level_fee" header="阶梯费用" style="min-width: 8rem">
          <template #body="slotProps">
            <Tag
              :severity="
                slotProps.data.is_level_fee === 1 ? 'success' : 'secondary'
              "
              :value="isLevelFeeMap[slotProps.data.is_level_fee]"
            />
          </template>
        </Column>

        <Column field="state" header="状态" style="min-width: 6rem">
          <template #body="slotProps">
            <Tag
              :severity="feeTemplateStateSeverityMap[slotProps.data.state]"
              :value="feeTemplateStateValueMap[slotProps.data.state]"
            />
          </template>
        </Column>

        <Column
          field="created_at"
          header="创建时间"
          style="min-width: 12rem; white-space: nowrap"
          frozen
          alignFrozen="right"
        >
          <template #body="slotProps">
            <div class="created-time">
              {{ formatDateTime(slotProps.data.created_at) }}
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template>

<style scoped>
/* Apple Design System - 费用模板页面样式 */
.fee-template-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

/* 苹果风格按钮 */
.apple-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  backdrop-filter: blur(10px) !important;
}

.apple-button.primary {
  background: linear-gradient(135deg, #007aff 0%, #0056d6 100%) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3) !important;
}

.apple-button.primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4) !important;
}

.apple-button.secondary {
  background: rgba(0, 122, 255, 0.1) !important;
  border: 1px solid rgba(0, 122, 255, 0.3) !important;
  color: #007aff !important;
}

.apple-button.secondary:hover {
  background: rgba(0, 122, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.apple-button.outlined {
  background: transparent !important;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  color: #333 !important;
}

.apple-button.outlined:hover {
  background: rgba(0, 0, 0, 0.05) !important;
  transform: translateY(-1px) !important;
}

/* 数据表格样式 */
:deep(.apple-datatable) {
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

:deep(.apple-datatable .p-datatable-thead > tr > th) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  color: #1d1d1f !important;
  font-weight: 600 !important;
  padding: 1rem 1.5rem !important;
  border: none !important;
  font-size: 0.875rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

:deep(.apple-datatable .p-datatable-tbody > tr) {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  border: none !important;
}

:deep(.apple-datatable .p-datatable-tbody > tr:hover) {
  background: rgba(0, 122, 255, 0.05) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

:deep(.apple-datatable .p-datatable-tbody > tr > td) {
  padding: 1rem 1.5rem !important;
  border: none !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* 表格内容样式 */
.template-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #1d1d1f;
}

.template-icon {
  color: #007aff;
  font-size: 0.875rem;
}

.bill-rule {
  color: #6e6e73;
  font-size: 0.875rem;
  line-height: 1.4;
}

.created-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6e6e73;
  font-size: 1rem;
}

.time-icon {
  color: #007aff;
  font-size: 0.75rem;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;
}

.action-button {
  width: 2.5rem !important;
  height: 2.5rem !important;
  border-radius: 50% !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  backdrop-filter: blur(10px) !important;
}

.action-button:hover {
  transform: scale(1.1) translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.view-button:hover {
  background: rgba(0, 122, 255, 0.1) !important;
  border-color: #007aff !important;
}

.edit-button:hover {
  background: rgba(255, 149, 0, 0.1) !important;
  border-color: #ff9500 !important;
}

.copy-button:hover {
  background: rgba(175, 82, 222, 0.1) !important;
  border-color: #af52de !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fee-template-container {
    padding: 1rem;
  }

  .filter-section {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-group {
    flex-direction: column;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

/* 滚动条样式 */
:deep(.p-datatable-scrollable-body)::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

:deep(.p-datatable-scrollable-body)::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

:deep(.p-datatable-scrollable-body)::-webkit-scrollbar-thumb {
  background: rgba(0, 122, 255, 0.3);
  border-radius: 3px;
}

:deep(.p-datatable-scrollable-body)::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 122, 255, 0.5);
}
</style>
