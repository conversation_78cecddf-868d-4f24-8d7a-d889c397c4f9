<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  getOrderDetail,
  getOrderFiles,
  previewOrderFile,
} from "../../services/income/order";
import { getFeePackagesSimpleList } from "../../services/feePackage";
import type { OrderItem, OrderFileItem } from "../../types/order";
import type { FeePackageSimpleItem } from "../../types/feePackage";
import { formatDateTime } from "../../utils/common";
import { useToast } from "primevue/usetoast";

const props = defineProps<{
  orderId: number;
}>();

const order = ref<OrderItem | null>(null);
const loading = ref(false);
const toast = useToast();
const feePackages = ref<FeePackageSimpleItem[]>([]);
const orderFiles = ref<OrderFileItem[]>([]);
const filesLoading = ref(false);

// 计算费用套餐名称
const feePackageName = computed(() => {
  if (!order.value?.income_fee_package_id || !feePackages.value.length) {
    return "未设置";
  }
  const feePackage = feePackages.value.find(
    (pkg) => pkg.id === order.value!.income_fee_package_id
  );
  return feePackage?.package_name || "未知套餐";
});

// 格式化货币显示
const formatCurrency = (amount: number, currency: string = "CNY") => {
  if (!amount && amount !== 0) return "未设置";
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: currency || "CNY",
    minimumFractionDigits: 2,
  }).format(amount);
};

// 格式化税率显示
const formatTaxRate = (rate: number) => {
  if (!rate && rate !== 0) return "未设置";
  return `${(rate)}%`;
};

const loadOrderDetail = async () => {
  try {
    loading.value = true;
    const response = await getOrderDetail(props.orderId);
    order.value = response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "获取订单详情失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 加载费用套餐列表
const loadFeePackages = async () => {
  try {
    const response = await getFeePackagesSimpleList();
    feePackages.value = response.data;
  } catch (error) {
    console.error("加载费用套餐列表失败:", error);
  }
};

// 加载订单文件列表
const loadOrderFiles = async () => {
  filesLoading.value = true;
  try {
    const response = await getOrderFiles(props.orderId);
    orderFiles.value = response.data;
  } catch (error) {
    console.error("加载订单文件失败:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载订单文件失败",
      life: 3000,
    });
  }
  filesLoading.value = false;
};

// 预览文件
const previewFile = async (file: OrderFileItem) => {
  try {
    const result = await previewOrderFile(props.orderId, file.id);
    // 创建预览链接并在新窗口打开
    const url = window.URL.createObjectURL(result.blob);
    window.open(url, "_blank");
    // 延迟释放URL对象
    setTimeout(() => {
      window.URL.revokeObjectURL(url);
    }, 1000);
  } catch (error: any) {
    console.error("预览文件失败:", error);
    toast.add({
      severity: "error",
      summary: "预览失败",
      detail: error.response?.data?.msg || `预览文件 "${file.file_name}" 失败`,
      life: 5000,
    });
  }
};

onMounted(async () => {
  await Promise.all([loadOrderDetail(), loadFeePackages(), loadOrderFiles()]);
});
</script>

<template>
  <div class="order-detail">
    <ProgressSpinner v-if="loading" class="loading-spinner" />
    <div v-else-if="order" class="order-detail-content">
      <!-- 基本信息区域 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-info-circle"></i>
          基本信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-5 gap-3">
            <div class="detail-item">
              <label>订单编号</label>
              <span class="detail-value">{{ order.total_num }}</span>
            </div>
            <div class="detail-item">
              <label>合同编号</label>
              <span class="detail-value">{{ order.contract_num }}</span>
            </div>
            <div class="detail-item">
              <label>合同法务编号</label>
              <span class="detail-value">{{ order.contract_legal_num || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>签约主体</label>
              <span class="detail-value">{{ order.sign_contract_entity || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>客户名称</label>
              <span class="detail-value">{{ order.customer_name || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>业务类型</label>
              <span class="detail-value">{{ order.business || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>类型</label>
              <span class="detail-value">{{ order.order_type }}</span>
            </div>
            <div class="detail-item">
              <label>服务状态</label>
              <span class="detail-value">{{ order.service_status }}</span>
            </div>
            <div class="detail-item">
              <label>计费状态</label>
              <span class="detail-value">{{ order.bill_status }}</span>
            </div>
            <div class="detail-item">
              <label>派工状态</label>
              <span class="detail-value">{{ order.job_status || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>服务类型</label>
              <span class="detail-value">{{ order.service_type }}</span>
            </div>
            <div class="detail-item">
              <label>收入分类</label>
              <span class="detail-value">{{ order.income_type }}</span>
            </div>
            <div class="detail-item">
              <label>产品主类</label>
              <span class="detail-value">{{
                order.product_main_category
              }}</span>
            </div>
            <div class="detail-item">
              <label>产品次类</label>
              <span class="detail-value">{{ order.product_sub_category }}</span>
            </div>
            <div class="detail-item">
              <label>付费周期</label>
              <span class="detail-value">{{ order.pay_cycle }}</span>
            </div>
            <div class="detail-item">
              <label>付费方式</label>
              <span class="detail-value">{{ order.pay_type }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 费用信息区域 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-dollar"></i>
          费用信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-5 gap-3">
            <div class="detail-item">
              <label>费用套餐</label>
              <span class="detail-value">{{ feePackageName }}</span>
            </div>
            <div class="detail-item">
              <label>一次性费用</label>
              <span class="detail-value">{{
                formatCurrency(order.once_fee, order.currency_type)
              }}</span>
            </div>
            <div class="detail-item">
              <label>周期性费用</label>
              <span class="detail-value">{{
                formatCurrency(order.cycle_fee, order.currency_type)
              }}</span>
            </div>
            <div class="detail-item">
              <label>税率</label>
              <span class="detail-value">{{
                formatTaxRate(order.tax_rate)
              }}</span>
            </div>
            <div class="detail-item">
              <label>税率类型</label>
              <span class="detail-value">{{ order.tax_type || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>货币类型</label>
              <span class="detail-value">{{
                order.currency_type || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>资费信息</label>
              <span class="detail-value">{{
                order.charge_explain || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 地址信息区域 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-map-marker"></i>
          地址信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-4 gap-3">
            <div class="detail-item">
              <label>A端信息</label>
              <span class="detail-value">{{ order.a_info || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>A端地址</label>
              <span class="detail-value">{{ order.a_address || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>Z端信息</label>
              <span class="detail-value">{{ order.z_info || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>Z端地址</label>
              <span class="detail-value">{{ order.z_address || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>合作商名称</label>
              <span class="detail-value">{{ order.partner_name || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>合作商编号</label>
              <span class="detail-value">{{
                order.partner_po_num || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 时间信息区域 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-calendar"></i>
          时间信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-5 gap-3">
            <div class="detail-item">
              <label>下单时间</label>
              <span class="detail-value">{{
                formatDateTime(order.created_at?.toString()) ||
                "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>新装要求完工日</label>
              <span class="detail-value">{{
                order.new_required_finished_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>实际计费始日</label>
              <span class="detail-value">{{
                order.reality_bill_start_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>实际开始实施时间</label>
              <span class="detail-value">{{
                formatDateTime(order.new_build_start_time?.toString()) || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>实施报完工时间</label>
              <span class="detail-value">{{
                formatDateTime(order.new_build_finished_time?.toString()) ||
                "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>收入报完工时间</label>
              <span class="detail-value">{{
                formatDateTime(order.new_build_bill_time?.toString()) || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>账务确认时间</label>
              <span class="detail-value">{{
                formatDateTime(order.new_build_charge_time?.toString()) || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>客服确认时间</label>
              <span class="detail-value">{{
                formatDateTime(order.new_build_support_time?.toString()) || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>拆机要求完工日</label>
              <span class="detail-value">{{
                order.remove_required_finished_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>实际计费终日</label>
              <span class="detail-value">{{
                order.reality_bill_end_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>拆机实施开始时间</label>
              <span class="detail-value">{{
                formatDateTime(order.remove_build_start_time?.toString()) ||
                "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>拆机实施报完工时间</label>
              <span class="detail-value">{{
                formatDateTime(order.remove_build_finished_time?.toString()) ||
                "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>拆机计费报完工时间</label>
              <span class="detail-value">{{
                formatDateTime(order.remove_build_bill_time?.toString()) || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>拆机账务复核时间</label>
              <span class="detail-value">{{
                formatDateTime(order.remove_build_charge_time?.toString()) ||
                "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>拆机维护确认时间</label>
              <span class="detail-value">{{
                formatDateTime(order.remove_build_support_time?.toString()) ||
                "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 文件信息区域 -->
      <div class="detail-section" v-if="orderFiles.length > 0">
        <h3 class="section-title">
          <i class="pi pi-file"></i>
          附件文件
        </h3>
        <Fluid>
          <div class="grid grid-cols-2 gap-4">
            <!-- 产品文件 -->
            <div class="file-section">
              <h4 class="file-section-title">产品文件</h4>
              <div
                v-if="filesLoading"
                class="flex justify-center items-center py-8"
              >
                <i class="pi pi-spin pi-spinner text-4xl text-blue-500"></i>
              </div>
              <div class="space-y-2">
                <div
                  v-for="file in orderFiles.filter(
                    (f) => f.file_class === 'product'
                  )"
                  :key="file.id"
                  class="file-item-detail"
                >
                  <div class="flex items-center gap-3">
                    <i class="pi pi-file-pdf text-red-500 text-xl"></i>
                    <span class="font-medium text-gray-900">{{
                      file.file_name
                    }}</span>
                  </div>
                  <Button
                    icon="pi pi-file"
                    severity="info"
                    outlined
                    rounded
                    @click="previewFile(file)"
                    v-tooltip.top="'预览文件'"
                  />
                </div>
                <div
                  v-if="
                    orderFiles.filter((f) => f.file_class === 'product')
                      .length === 0
                  "
                  class="text-center py-8 text-gray-500"
                >
                  <i class="pi pi-inbox text-4xl mb-2 block"></i>
                  <p>暂无产品文件</p>
                </div>
              </div>
            </div>

            <!-- 完工文件 -->
            <div class="file-section">
              <h4 class="file-section-title">完工文件</h4>
              <div class="space-y-2">
                <div
                  v-for="file in orderFiles.filter(
                    (f) => f.file_class === 'finished'
                  )"
                  :key="file.id"
                  class="file-item-detail"
                >
                  <div class="flex items-center gap-3">
                    <i class="pi pi-file-pdf text-orange-500 text-xl"></i>
                    <span class="font-medium text-gray-900">{{
                      file.file_name
                    }}</span>
                  </div>
                  <Button
                    icon="pi pi-file"
                    severity="info"
                    outlined
                    rounded
                    @click="previewFile(file)"
                    v-tooltip.top="'预览文件'"
                  />
                </div>
                <div
                  v-if="
                    orderFiles.filter((f) => f.file_class === 'finished')
                      .length === 0
                  "
                  class="text-center py-8 text-gray-500"
                >
                  <i class="pi pi-inbox text-4xl mb-2 block"></i>
                  <p>暂无完工文件</p>
                </div>
              </div>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 备注信息区域 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-comment"></i>
          备注信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>订单备注</label>
              <div class="detail-value">{{ order.order_remark || "--" }}</div>
            </div>
            <div class="detail-item">
              <label>完工备注</label>
              <div class="detail-value">
                {{ order.finished_remark || "--" }}
              </div>
            </div>
            <div class="detail-item">
              <label>资费备注</label>
              <div class="detail-value">{{ order.charge_remark || "--" }}</div>
            </div>
          </div>
        </Fluid>
      </div>
    </div>
  </div>
</template>

<style scoped>
.order-detail {
  height: calc(100vh - 18rem);
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.order-detail-content {
  max-height: none;
  background: #fbfbfd;
  overflow-y: auto;
  height: calc(100vh - 18rem);
}

.detail-section {
  margin-bottom: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--p-primary-color);
}

.section-title i {
  color: var(--p-primary-color);
  font-size: 1rem;
}

.detail-section .p-fluid {
  padding: 0.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #6e6e73;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-detail {
    padding: 1rem;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .detail-section .p-fluid {
    padding: 1rem;
  }

  .section-title {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

/* 滚动条样式 */
.order-detail-content::-webkit-scrollbar {
  width: 6px;
}

.order-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.order-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.order-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 文件相关样式 */
.file-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.file-section-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-section-title::before {
  content: "";
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  border-radius: 8px;
}

.file-item-detail {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background: var(--surface-ground);
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.file-item-detail:hover {
  background: var(--surface-hover);
  border-color: var(--primary-color);
}
</style>
