<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useToast } from "primevue/usetoast";
import {
  getBankStatementDetail,
  getReceiveStatements,
  getRecognitionDetails,
  submitRecognition,
  getInvoiceRecognition,
} from "../../services/bankStatement";
import type {
  BankStatementItem,
  ReceiveStatementItem,
  RecognitionDetailItem,
  RecognitionDetailParams,
  RecognitionRequest,
  RecognitionRequestItem,
  InvoiceRecognitionItem,
} from "../../types/bankStatement";
import { formatDateTime, formatCurrency } from "../../utils/common";
import UnrecognizedTab from "./UnrecognizedTab.vue";
import PrepaidTab from "./PrepaidTab.vue";

// Props
const props = defineProps<{
  bankStatementId: number;
}>();

const toast = useToast();

// 银行流水详情
const bankStatementDetail = ref<BankStatementItem | null>(null);
const loadingDetail = ref(false);

// 右侧Tab管理
const activeRightTab = ref("received");

// Tab激活状态管理
const tabActivated = ref({
  received: false,
  pending: false,
  prepaid: false,
  unrecognized: false,
});

// 监听Tab切换，加载对应数据
watch(activeRightTab, (newTab) => {
  switch (newTab) {
    case "received":
      if (!tabActivated.value.received) {
        loadReceiveStatements();
        tabActivated.value.received = true;
      }
      break;
    case "pending":
      if (!tabActivated.value.pending) {
        loadRecognitionDetails();
        loadInvoiceRecognition();
        tabActivated.value.pending = true;
      }
      break;
    case "prepaid":
      tabActivated.value.prepaid = true;
      break;
    case "unrecognized":
      tabActivated.value.unrecognized = true;
      break;
  }
});

// 已认款数据
const receiveStatements = ref<ReceiveStatementItem[]>([]);
const receiveLoading = ref(false);
const receiveTotalRecords = ref(0);
const receiveLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 待认款数据
const recognitionDetails = ref<RecognitionDetailItem[]>([]);
const selectedRecognitionDetails = ref<RecognitionDetailItem[]>([]);
const recognitionLoading = ref(false);
const recognitionTotalRecords = ref(0);
const recognitionLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 待认款筛选参数
const recognitionFilters = ref({
  account_seq: "",
  adjust_month: "",
  charge_month: "",
  sub_order_no: "",
});

// 认款金额输入框数据
const recognitionAmounts = ref<Record<number, number>>({});

// 计算当前选中的认款总金额
const selectedAmount = computed(() => {
  return selectedRecognitionDetails.value.reduce((total, item) => {
    const amount = recognitionAmounts.value[item.id] || 0;
    return total + amount;
  }, 0);
});

// 计算当前认款总金额
const currentRecognitionAmount = computed(() => {
  const selectedAmount = selectedRecognitionDetails.value.reduce(
    (total, item) => {
      const amount = recognitionAmounts.value[item.id] || 0;
      return total + amount;
    },
    0
  );

  // 加上银行流水详情中的确认中金额
  const bankConfirmingAmount = bankStatementDetail.value
    ? parseFloat(bankStatementDetail.value.confirming_amount) || 0
    : 0;

  // 加上预付费认款金额
  const prepaidAmount = prepaidRecognitionAmount.value || 0;

  return selectedAmount + bankConfirmingAmount + prepaidAmount;
});

// 计算剩余金额
const remainingAmount = computed(() => {
  if (!bankStatementDetail.value) return 0;
  const totalAmount = parseFloat(bankStatementDetail.value.amount) || 0;
  return totalAmount - currentRecognitionAmount.value;
});

// 计算进度百分比
const progressPercentage = computed(() => {
  if (!bankStatementDetail.value) return 0;
  const totalAmount = parseFloat(bankStatementDetail.value.amount) || 0;
  if (totalAmount === 0) return 0;
  return (currentRecognitionAmount.value / totalAmount) * 100;
});

// 计算认款按钮是否应该禁用
const isRecognitionDisabled = computed(() => {
  // 没有选中任何项
  if (selectedRecognitionDetails.value.length === 0) return true;

  // 检查选中的认款记录中是否有零值金额
  const hasZeroAmount = selectedRecognitionDetails.value.some((item) => {
    const amount = recognitionAmounts.value[item.id] || 0;
    return amount === 0;
  });
  if (hasZeroAmount) return true;

  // 当前认款金额大于银行流水总金额
  if (!bankStatementDetail.value) return true;
  const totalAmount = parseFloat(bankStatementDetail.value.amount) || 0;
  return currentRecognitionAmount.value > totalAmount;
});

// 认款提交状态
const isSubmittingRecognition = ref(false);

// 确认对话框状态
const confirmDialogVisible = ref(false);

// 未认款刷新触发器
const unrecognizedRefreshKey = ref(0);

// 预付费认款金额
const prepaidRecognitionAmount = ref(0);

// 发票认款数据
const invoiceRecognitionData = ref<InvoiceRecognitionItem[]>([]);
const invoiceRecognitionLoading = ref(false);

// 监听选中项变化，确保选中项有正确的默认值
watch(
  selectedRecognitionDetails,
  (newSelection) => {
    newSelection.forEach((item) => {
      if (!(item.id in recognitionAmounts.value)) {
        const unpayAmount = parseFloat(item.unpay_amount) || 0;
        const confirmingAmount = typeof item.confirming_amount === 'number' 
          ? item.confirming_amount 
          : parseFloat(item.confirming_amount) || 0;
        recognitionAmounts.value[item.id] = unpayAmount - confirmingAmount;
      }
    });
  },
  { deep: true }
);

// 加载银行流水详情
const loadBankStatementDetail = async () => {
  loadingDetail.value = true;
  try {
    const response = await getBankStatementDetail(props.bankStatementId);
    bankStatementDetail.value = response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载银行流水详情失败",
      life: 3000,
    });
  } finally {
    loadingDetail.value = false;
  }
};

// 加载已认款列表
const loadReceiveStatements = async () => {
  receiveLoading.value = true;
  try {
    const response = await getReceiveStatements(props.bankStatementId, {
      page: receiveLazyParams.value.page,
      pageSize: receiveLazyParams.value.pageSize,
    });
    receiveStatements.value = response.data.records;
    receiveTotalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载已认款列表失败",
      life: 3000,
    });
  } finally {
    receiveLoading.value = false;
  }
};

// 加载发票认款数据
const loadInvoiceRecognition = async () => {
  if (!bankStatementDetail.value?.customer_num) return;
  
  invoiceRecognitionLoading.value = true;
  try {
    const response = await getInvoiceRecognition(bankStatementDetail.value.customer_num);
    invoiceRecognitionData.value = response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载发票认款数据失败",
      life: 3000,
    });
  } finally {
    invoiceRecognitionLoading.value = false;
  }
};

// 加载待认款列表
const loadRecognitionDetails = async () => {
  recognitionLoading.value = true;
  try {
    const params: RecognitionDetailParams = {
      page: recognitionLazyParams.value.page,
      pageSize: recognitionLazyParams.value.pageSize,
    };

    // 添加筛选参数
    if (recognitionFilters.value.account_seq.trim()) {
      params.account_seq = recognitionFilters.value.account_seq.trim();
    }
    if (recognitionFilters.value.adjust_month.trim()) {
      params.adjust_month = recognitionFilters.value.adjust_month.trim();
    }
    if (recognitionFilters.value.charge_month.trim()) {
      params.charge_month = recognitionFilters.value.charge_month.trim();
    }
    if (recognitionFilters.value.sub_order_no.trim()) {
      params.order_no = recognitionFilters.value.sub_order_no.trim();
    }

    const response = await getRecognitionDetails(props.bankStatementId, params);
    recognitionDetails.value = response.data.records;
    recognitionTotalRecords.value = response.data.page.total;

    // 为所有行初始化默认的权责金额值
    response.data.records.forEach((item) => {
      if (!(item.id in recognitionAmounts.value)) {
        const unpayAmount = parseFloat(item.unpay_amount) || 0;
        const confirmingAmount = typeof item.confirming_amount === 'number' 
          ? item.confirming_amount 
          : parseFloat(item.confirming_amount) || 0;
        recognitionAmounts.value[item.id] = unpayAmount - confirmingAmount;
      }
    });
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载待认款列表失败",
      life: 3000,
    });
  } finally {
    recognitionLoading.value = false;
  }
};

// 处理已认款分页
const onReceivePage = (event: { page: number; rows: number }) => {
  receiveLazyParams.value.page = event.page + 1;
  receiveLazyParams.value.pageSize = event.rows;
  loadReceiveStatements();
};

// 处理待认款分页
const onRecognitionPage = (event: { page: number; rows: number }) => {
  recognitionLazyParams.value.page = event.page + 1;
  recognitionLazyParams.value.pageSize = event.rows;
  loadRecognitionDetails();
};

// 搜索待认款
const handleRecognitionSearch = () => {
  recognitionLazyParams.value.page = 1;
  loadRecognitionDetails();
};

// 重置待认款筛选
const resetRecognitionFilters = () => {
  recognitionFilters.value = {
    account_seq: "",
    adjust_month: "",
    charge_month: "",
    sub_order_no: "",
  };
  recognitionLazyParams.value.page = 1;
  loadRecognitionDetails();
};

// 显示确认对话框
const showConfirmDialog = () => {
  if (isRecognitionDisabled.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "当前认款金额不能大于银行流水总金额",
      life: 3000,
    });
    return;
  }

  confirmDialogVisible.value = true;
};

// 提交认款
const handleRecognitionSubmit = async () => {
  isSubmittingRecognition.value = true;
  try {
    // 构建请求数据
    const receiveStatements: RecognitionRequestItem[] =
      selectedRecognitionDetails.value.map((item) => ({
        amount: (recognitionAmounts.value[item.id] || 0).toString(),
        sub_order_no: item.sub_order_no,
        income_charge_detail_id: item.id,
      }));

    const requestData: RecognitionRequest = {
      receive_statement: receiveStatements,
    };

    await submitRecognition(props.bankStatementId, requestData);

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "认款提交成功",
      life: 3000,
    });

    // 清空选中项和认款金额
    selectedRecognitionDetails.value = [];

    // 重新加载数据
    loadBankStatementDetail();
    loadRecognitionDetails();
    // 刷新未认款数据
    handleUnrecognizedRefresh();
  } catch (error: any) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: error.response?.data?.message || "认款提交失败",
      life: 3000,
    });
  } finally {
    isSubmittingRecognition.value = false;
    confirmDialogVisible.value = false;
  }
};

// 格式化日期为yyyymm格式
const formatDateToYYYYMM = (date: Date | null): string => {
  if (!date) return "";
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `${year}${month}`;
};

// 日期变化处理
const onAdjustMonthChange = (date: Date | null) => {
  recognitionFilters.value.adjust_month = formatDateToYYYYMM(date);
};

const onChargeMonthChange = (date: Date | null) => {
  recognitionFilters.value.charge_month = formatDateToYYYYMM(date);
};

// 处理未认款Tab的数据刷新请求
const handleUnrecognizedRefresh = () => {
  // 重新加载银行流水详情
  loadBankStatementDetail();
  // 重新加载待认款列表
  loadRecognitionDetails();
  // 触发未认款组件刷新
  unrecognizedRefreshKey.value++;
};

// 处理预付费认款金额更新
const handlePrepaidAmountUpdate = (amount: number) => {
  prepaidRecognitionAmount.value = amount;
};

onMounted(() => {
  // 进入页面时默认加载银行流水详情和已认款列表
  loadBankStatementDetail();
  loadReceiveStatements();
  // 标记已认款Tab为已激活
  tabActivated.value.received = true;
});
</script>

<template>
  <div class="recognition-detail-container">
    <!-- 使用Splitter组件分栏显示 -->
    <Splitter style="height: calc(100vh - 17rem)">
      <!-- 左侧：银行流水详情 (20%) -->
      <SplitterPanel :size="20" :minSize="15">
        <div class="card h-full" style="display: flex; flex-direction: column;">
          <div class="panel-header">
            <h3 class="panel-title">
              <i class="pi pi-info-circle"></i>
              银行流水详情
            </h3>
          </div>
          <div class="panel-content" v-if="bankStatementDetail">
            <div class="summary-content">
              <div class="summary-item">
                <label>总金额</label>
                <span class="amount-value total-amount">
                  {{
                    formatCurrency(
                      bankStatementDetail.amount,
                      bankStatementDetail.currency_type
                    )
                  }}
                </span>
              </div>
              <div class="summary-item">
                <label>当前认款金额</label>
                <span class="amount-value current-amount">
                  {{
                    formatCurrency(
                      currentRecognitionAmount.toString(),
                      bankStatementDetail.currency_type
                    )
                  }}
                </span>
              </div>
              <div class="summary-item">
                <label>剩余金额</label>
                <span class="amount-value remaining-amount">
                  {{
                    formatCurrency(
                      remainingAmount.toString(),
                      bankStatementDetail.currency_type
                    )
                  }}
                </span>
              </div>
              <div class="progress-section">
                <div class="progress-header">
                  <span class="progress-label">认款金额占比</span>
                  <span
                    class="progress-percentage"
                    :style="{
                      color:
                        progressPercentage > 100
                          ? '#ff3b30'
                          : progressPercentage === 100
                          ? '#34c759'
                          : '#007aff',
                    }"
                    >{{ progressPercentage.toFixed(1) }}%</span
                  >
                </div>
                <ProgressBar
                  class="recognition-progress"
                  :class="{
                    'progress-complete': progressPercentage === 100,
                    'progress-overflow': progressPercentage > 100,
                  }"
                  :showValue="false"
                  :value="Math.min(progressPercentage, 100)"
                />
              </div>
              <div class="summary-item">
                <label>流水号</label>
                <span class="detail-value">{{
                  bankStatementDetail.statement_no
                }}</span>
              </div>
              <div class="summary-item">
                <label>交易日期</label>
                <span class="detail-value">{{
                  bankStatementDetail.statement_date
                }}</span>
              </div>
              <div class="summary-item">
                <label>付款方名称</label>
                <span class="detail-value">{{
                  bankStatementDetail.payment_name
                }}</span>
              </div>
              <div class="summary-item">
                <label>付款银行</label>
                <span class="detail-value">{{
                  bankStatementDetail.payment_bank_name
                }}</span>
              </div>
              <div class="summary-item">
                <label>付款账号</label>
                <span class="detail-value">{{
                  bankStatementDetail.payment_bank_no
                }}</span>
              </div>
              <div class="summary-item">
                <label>收款银行</label>
                <span class="detail-value">{{
                  bankStatementDetail.receive_bank_name
                }}</span>
              </div>
              <div class="summary-item">
                <label>收款账号</label>
                <span class="detail-value">{{
                  bankStatementDetail.receive_bank_no
                }}</span>
              </div>
              <div class="summary-item">
                <label>客户名称</label>
                <span class="detail-value">{{
                  bankStatementDetail.customer_name || "--"
                }}</span>
              </div>
              <div class="summary-item" v-if="bankStatementDetail.description">
                <label>摘要</label>
                <span class="detail-value">{{
                  bankStatementDetail.description
                }}</span>
              </div>
            </div>
          </div>
          <div class="panel-content" v-else>
            <div class="loading-placeholder">
              <ProgressSpinner v-if="loadingDetail" />
              <p v-else>暂无详情数据</p>
            </div>
          </div>
        </div>
      </SplitterPanel>

      <!-- 右侧：认款信息Tab (80%) -->
      <SplitterPanel :size="80" :minSize="80">
        <div class="card h-full">
          <Tabs
            :value="activeRightTab"
            @update:value="activeRightTab = $event as string"
          >
            <TabList>
              <Tab value="received">已认款</Tab>
              <Tab value="pending">后付费认款</Tab>
              <Tab value="prepaid">预付费认款</Tab>
              <Tab value="unrecognized">未确认认款</Tab>
            </TabList>
            <TabPanels class="p-0">
              <!-- 已认款Tab -->
              <TabPanel value="received">
                <DataTable
                :value="receiveStatements"
                :lazy="true"
                :paginator="true"
                :rows="20"
                :rowsPerPageOptions="[10, 20, 50]"
                :totalRecords="receiveTotalRecords"
                :loading="receiveLoading"
                @page="onReceivePage($event)"
                paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
                class="p-datatable-sm"
                showGridlines
                stripedRows
                scrollable
                scrollHeight="calc(100vh - 35rem)"
                resizableColumns
                columnResizeMode="fit"
              >
                <template #empty>
                  <div class="empty-message">
                    <i
                      class="pi pi-inbox"
                      style="
                        font-size: 2rem;
                        color: var(--p-text-color-secondary);
                        margin-bottom: 1rem;
                      "
                    ></i>
                    <p>暂无已认款数据</p>
                  </div>
                </template>
                <Column field="id" header="ID" style="min-width: 5rem" />
                <Column
                  field="bank_statement_no"
                  header="银行流水号"
                  style="min-width: 15rem"
                />
                <Column
                  field="created_at"
                  header="认款时间"
                  style="min-width: 12rem"
                >
                  <template #body="slotProps">
                    <span>{{ formatDateTime(slotProps.data.created_at) }}</span>
                  </template>
                </Column>
                </DataTable>
              </TabPanel>

              <!-- 待认款Tab -->
              <TabPanel value="pending">
                <!-- 使用Splitter组件分栏显示 -->
                <Splitter style="height: calc(100vh - 24rem)">
                  <!-- 左侧：发票信息 (20%) -->
                  <SplitterPanel :size="20" :minSize="15">
                    <div class="card h-full" style="display: flex; flex-direction: column;">
                      <div class="panel-content" style="overflow-y: auto;">
                        <div v-if="invoiceRecognitionLoading" class="loading-placeholder">
                          <ProgressSpinner />
                          <p>加载发票数据中...</p>
                        </div>
                        <div v-else-if="invoiceRecognitionData.length === 0" class="empty-message">
                          <i class="pi pi-inbox" style="font-size: 2rem; color: var(--p-text-color-secondary); margin-bottom: 1rem;"></i>
                          <p>暂无发票认款数据</p>
                        </div>
                        <div v-else class="invoice-list">
                          <div v-for="invoice in invoiceRecognitionData" :key="invoice.id" class="invoice-item">
                            <div class="invoice-no">
                              {{ invoice.invoice_no }}
                            </div>
                            <div class="invoice-amount">
                              {{ formatCurrency(invoice.current_amount, invoice.currency_type) }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </SplitterPanel>

                  <!-- 右侧：权责信息 (80%) -->
                  <SplitterPanel :size="80" :minSize="80">
                    <div class="card h-full">
                      <div class="panel-content" style="overflow: hidden;">
                        <!-- 筛选区域 -->
                        <Toolbar class="mb-2">
                        <template #end>
                          <div class="flex flex-wrap align-items-center gap-2">
                            <FloatLabel>
                              <label for="filterOrderNo">合成编号</label>
                              <InputText
                                id="filterOrderNo"
                                v-model="recognitionFilters.sub_order_no"
                              />
                            </FloatLabel>
                            <FloatLabel>
                              <label for="filterAccountSeq">分账序号</label>
                              <InputText
                                id="filterAccountSeq"
                                v-model="recognitionFilters.account_seq"
                              />
                            </FloatLabel>
                            <FloatLabel>
                              <label for="filterChargeMonth">权责账期</label>
                              <DatePicker
                                id="filterChargeMonth"
                                view="month"
                                dateFormat="yy-mm"
                                @date-select="onChargeMonthChange"
                              />
                            </FloatLabel>
                            <FloatLabel class="mr-2">
                              <label for="filterAdjustMonth">调整账期</label>
                              <DatePicker
                                id="filterAdjustMonth"
                                view="month"
                                dateFormat="yy-mm"
                                @date-select="onAdjustMonthChange"
                              />
                            </FloatLabel>
                          </div>
                          <Button
                            icon="pi pi-search"
                            rounded
                            @click="handleRecognitionSearch"
                            class="mr-2"
                          />
                          <Button
                            icon="pi pi-refresh"
                            rounded
                            @click="resetRecognitionFilters"
                            outlined
                          />
                          <Divider layout="vertical" />
                          <Button
                            icon="fa fa-regular fa-handshake"
                            @click="showConfirmDialog"
                            :disabled="isRecognitionDisabled"
                            :loading="isSubmittingRecognition"
                            severity="warn"
                          />
                        </template>
                      </Toolbar>

                      <DataTable
                        :value="recognitionDetails"
                        v-model:selection="selectedRecognitionDetails"
                        :lazy="true"
                        :paginator="true"
                        :rows="20"
                        :rowsPerPageOptions="[10, 20, 50]"
                        :totalRecords="recognitionTotalRecords"
                        :loading="recognitionLoading"
                        @page="onRecognitionPage($event)"
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="显示第 {first} 到 {last} 条记录，共 {totalRecords} 条"
                        showGridlines
                        stripedRows
                        scrollable
                        scrollHeight="calc(100vh - 36rem)"
                        resizableColumns
                        columnResizeMode="fit"
                      >
                        <template #empty>
                          <div class="empty-message">
                            <i
                              class="pi pi-inbox"
                              style="
                                font-size: 2rem;
                                color: var(--p-text-color-secondary);
                                margin-bottom: 1rem;
                              "
                            ></i>
                            <p>暂无待认款数据</p>
                          </div>
                        </template>
                        <Column
                          selectionMode="multiple"
                          headerStyle="width: 3rem"
                          alignFrozen="left"
                          frozen
                        />
                        <Column
                          header="核销金额"
                          style="min-width: 8rem"
                          alignFrozen="left"
                          frozen
                        >
                          <template #body="slotProps">
                            <InputNumber
                              v-model="recognitionAmounts[slotProps.data.id]"
                              :disabled="
                                !selectedRecognitionDetails.some(
                                  (item) => item.id === slotProps.data.id
                                )
                              "
                              mode="currency"
                              :currency="slotProps.data.currency_type"
                              :min="0"
                              :max="
                                parseFloat(slotProps.data.unpay_amount) -
                                (typeof slotProps.data.confirming_amount === 'number' 
                                  ? slotProps.data.confirming_amount 
                                  : parseFloat(slotProps.data.confirming_amount))
                              "
                              :placeholder="
                                formatCurrency(
                                  (
                                    parseFloat(slotProps.data.unpay_amount) -
                                    (typeof slotProps.data.confirming_amount === 'number' 
                                      ? slotProps.data.confirming_amount 
                                      : parseFloat(slotProps.data.confirming_amount))
                                  ).toString(),
                                  slotProps.data.currency_type
                                )
                              "
                              class="recognition-amount-input"
                            />
                          </template>
                        </Column>
                        <Column
                          field="sub_order_no"
                          header="合成编号"
                          style="min-width: 12rem"
                        />
                        <Column
                          field="account_seq"
                          header="分账序号"
                          style="min-width: 10rem"
                        />
                        <Column
                          field="charge_month"
                          header="权责账期"
                          style="min-width: 8rem"
                        />
                        <Column
                          field="adjust_month"
                          header="调整账期"
                          style="min-width: 10rem"
                        />
                        <Column
                          field="pay_type"
                          header="付费方式"
                          style="min-width: 8rem"
                        />
                        <Column
                          field="fee_amount"
                          header="权责金额"
                          style="min-width: 10rem"
                        >
                          <template #body="slotProps">
                            <span class="font-semibold text-red-500">{{
                              formatCurrency(
                                slotProps.data.fee_amount,
                                slotProps.data.currency_type
                              )
                            }}</span>
                          </template>
                        </Column>
                        <Column
                          field="confirming_amount"
                          header="确认中金额"
                          style="min-width: 10rem"
                        >
                          <template #body="slotProps">
                            <span class="font-semibold text-primary">{{
                              formatCurrency(
                                (typeof slotProps.data.confirming_amount === 'number' 
                                  ? slotProps.data.confirming_amount 
                                  : parseFloat(slotProps.data.confirming_amount)).toString(),
                                slotProps.data.currency_type
                              )
                            }}</span>
                          </template>
                        </Column>
                        <Column
                          field="pay_amount"
                          header="已核销金额"
                          style="min-width: 10rem"
                        >
                          <template #body="slotProps">
                            <span class="font-semibold text-blue-500">{{
                              formatCurrency(
                                slotProps.data.pay_amount,
                                slotProps.data.currency_type
                              )
                            }}</span>
                          </template>
                        </Column>
                        <Column
                          field="unpay_amount"
                          header="未核销金额"
                          style="min-width: 10rem"
                        >
                          <template #body="slotProps">
                            <span class="font-semibold text-yellow-600">{{
                              formatCurrency(
                                slotProps.data.unpay_amount,
                                slotProps.data.currency_type
                              )
                            }}</span>
                          </template>
                        </Column>
                        <Column
                          field="income_type"
                          header="收入类型"
                          style="min-width: 8rem"
                        />
                        <Column
                          field="tax_type"
                          header="税收类型"
                          style="min-width: 8rem"
                        />
                        <Column
                          field="invoice_no"
                          header="发票号"
                          style="min-width: 12rem"
                        />
                      </DataTable>
                      </div>
                    </div>
                  </SplitterPanel>
                </Splitter>
              </TabPanel>

              <!-- 预付费认款Tab -->
              <TabPanel value="prepaid">
                <PrepaidTab
                  v-if="tabActivated.prepaid"
                  :bankStatementId="bankStatementId"
                  :bankStatementDetail="bankStatementDetail"
                  :refreshKey="unrecognizedRefreshKey"
                  @refreshData="handleUnrecognizedRefresh"
                  @updatePrepaidAmount="handlePrepaidAmountUpdate"
                />
              </TabPanel>

              <!-- 未认款Tab -->
              <TabPanel value="unrecognized">
                <UnrecognizedTab
                  v-if="tabActivated.unrecognized"
                  :bankStatementId="bankStatementId"
                  :refreshKey="unrecognizedRefreshKey"
                  @refreshData="handleUnrecognizedRefresh"
                />
              </TabPanel>
            </TabPanels>
          </Tabs>
        </div>
      </SplitterPanel>
    </Splitter>

    <!-- 确认认款对话框 -->
    <Dialog
      v-model:visible="confirmDialogVisible"
      modal
      header="确认认款"
      :style="{ width: '25rem' }"
      :closable="!isSubmittingRecognition"
      :closeOnEscape="!isSubmittingRecognition"
    >
      <div class="confirmation-content">
        <div class="mb-3 flex items-center">
          <i
            class="pi pi-exclamation-triangle text-orange-500 mr-2"
            style="font-size: 2rem"
          ></i>
          <span class="font-semibold flex">确定要提交认款吗？</span>
        </div>
        <div class="confirmation-details">
          <div class="detail-item">
            <label>认款金额：</label>
            <span class="font-semibold text-primary">
              {{
                formatCurrency(
                  selectedAmount.toString(),
                  bankStatementDetail?.currency_type || "CNY"
                )
              }}
            </span>
          </div>
          <div class="detail-item">
            <label>选中项目：</label>
            <span class="font-semibold"
              >{{ selectedRecognitionDetails.length }}项</span
            >
          </div>
        </div>
      </div>

      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          @click="confirmDialogVisible = false"
          :disabled="isSubmittingRecognition"
          text
        />
        <Button
          label="确认认款"
          icon="pi pi-check"
          @click="handleRecognitionSubmit"
          :loading="isSubmittingRecognition"
          severity="warn"
        />
      </template>
    </Dialog>
  </div>
</template>

<style scoped>
.recognition-detail-container {
  background: #f8f9fa;
}

.card {
  background: white;
  border-radius: 6px;
}

/* Splitter样式 */
:deep(.p-splitter) {
  border: none;
  background: transparent;
}

:deep(.p-splitter-panel) {
  background: transparent;
}

:deep(.p-splitter-gutter) {
  background: var(--p-surface-border);
  transition: background-color 0.2s;
}

:deep(.p-splitter-gutter:hover) {
  background: var(--p-primary-500);
}

:deep(.p-splitter-gutter-handle) {
  background: transparent;
}

.panel-header {
  background: var(--p-surface-ground);
  border-bottom: 1px solid var(--surface-border);
  padding: 0.9rem;
}

.panel-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.panel-content {
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--p-text-color-secondary);
}

.recognition-tabs {
  height: calc(100vh - 20rem);
}

:deep(.recognition-tabs .p-tabview-panels) {
  padding: 1rem;
  height: calc(100% - 3rem);
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  background: white;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.summary-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.summary-item label {
  font-size: 0.8125rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.amount-value {
  font-size: 1.25rem;
  font-weight: 700;
  font-variant-numeric: tabular-nums;
}

.detail-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  font-variant-numeric: tabular-nums;
}

.total-amount {
  color: #007aff;
}

.current-amount {
  color: #34c759;
}

.remaining-amount {
  color: #ff9500;
}

.progress-section {
  background: white;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.progress-label {
  font-size: 0.8125rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.progress-percentage {
  font-size: 0.875rem;
  font-weight: 700;
  color: #007aff;
  font-variant-numeric: tabular-nums;
  transition: color 0.3s ease;
}

.recognition-progress {
  height: 10px;
  border-radius: 3px;
  overflow: hidden;
}

/* 默认进度条样式 */
:deep(.recognition-progress .p-progressbar-value) {
  background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

/* 完成状态样式 */
:deep(.recognition-progress.progress-complete .p-progressbar-value) {
  background: linear-gradient(90deg, #34c759 0%, #30d158 100%) !important;
}

/* 超出状态样式 */
:deep(.recognition-progress.progress-overflow .p-progressbar-value) {
  background: linear-gradient(90deg, #ff3b30 0%, #ff6b6b 100%) !important;
}

@keyframes pulse-warning {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 认款金额输入框样式 */
.recognition-amount-input {
  width: 100%;
}

:deep(.recognition-amount-input .p-inputnumber-input) {
  font-size: 0.875rem;
  padding: 0.5rem;
}

/* 响应式设计 - 苹果设计规范 */
@media (max-width: 1200px) {
  .unified-summary {
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .panel-content {
    padding: 1rem;
  }

  .unified-summary {
    gap: 0.75rem;
  }

  .summary-item {
    padding: 0.75rem;
  }

  .progress-section {
    padding: 0.75rem;
  }

  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.375rem;
  }

  .amount-value {
    font-size: 1.125rem;
  }

  .detail-value {
    font-size: 0.9rem;
  }
}

/* 确认对话框样式 */
.confirmation-content {
  padding: 0.5rem 0;
}

.confirmation-details {
  background: var(--p-surface-50);
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid var(--p-primary-color);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  color: var(--p-text-color-secondary);
  font-weight: 500;
}

/* 发票认款列表样式 */
.invoice-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.invoice-item {
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.invoice-item:hover {
  border-color: var(--p-primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.invoice-no {
  font-size: 0.875rem;
  color: var(--p-text-color);
  margin-bottom: 0.5rem;
  font-weight: 500;
  word-break: break-all;
}

.invoice-amount {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--p-primary-color);
  text-align: right;
}
</style>
