<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import { getMenus } from "../../services/menu";
import type { Menu } from "../../types/menu";
import type { TreeNode } from "primevue/treenode";

const menus = ref<TreeNode[]>([]);
const loading = ref(false);
const toast = useToast();

// 转换菜单数据为 TreeNode 格式
const convertToTreeNodes = (data: Menu[]): TreeNode[] => {
  return data.map((menu) => ({
    key: menu.id.toString(),
    data: {
      menu_name: menu.menu_name,
      router: menu.router,
      icon: menu.icon,
      created_at: menu.created_at,
    },
    children: menu.children ? convertToTreeNodes(menu.children) : undefined,
  }));
};

// 加载菜单数据
const loadMenus = async () => {
  try {
    loading.value = true;
    const response = await getMenus();
    menus.value = convertToTreeNodes(response.data);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载菜单数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadMenus();
});
</script>

<template>
  <div class="menu-list-container">
    <div class="card">
      <TreeTable
        :value="menus"
        :loading="loading"
        size="large"
        :paginator="true"
        :rows="10"
        :rowsPerPageOptions="[10, 20, 50]"
        :resizableColumns="true"
        showGridlines
        stripedRows
      >
        <Column field="menu_name" header="菜单名称" expander></Column>
        <Column field="router" header="路由">
          <template #body="slotProps">
            <span>{{ slotProps.node.data.router || "--" }}</span>
          </template>
        </Column>
        <Column field="icon" header="图标">
          <template #body="slotProps">
            <i :class="slotProps.node.data.icon" style="color: green"></i>
          </template>
        </Column>
        <Column field="created_at" header="创建时间">
          <template #body="slotProps">
            {{ new Date(slotProps.node.data.created_at).toLocaleString() }}
          </template>
        </Column>
      </TreeTable>
    </div>
  </div>
</template>

<style scoped>
.menu-list-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}
</style>
