import { ref } from "vue";
import { useToast } from "primevue/usetoast";
import { getCustomersSimpleList } from "../services/customer";

export interface CustomerOption {
  label: string;
  value: string;
}

export interface CustomerOptionWithId extends CustomerOption {
  customer_id: number;
}

export interface CustomerRawData {
  customer_name: string;
  customer_num: string;
  id: number;
}

export type LabelFormat = "name-with-number" | "name-only" | "raw-data";

export function useCustomerOptions(labelFormat: LabelFormat = "name-with-number") {
  const toast = useToast();
  
  const customerOptions = ref<CustomerOption[]>([]);
  const loading = ref(false);

  const loadCustomerOptions = async () => {
    try {
      loading.value = true;
      const response = await getCustomersSimpleList();
      
      if (labelFormat === "raw-data") {
        (customerOptions as any).value = response.data;
      } else if (labelFormat === "name-only") {
        customerOptions.value = response.data.map((item) => ({
          label: item.customer_name,
          value: item.customer_num,
        }));
      } else {
        // default: "name-with-number"
        customerOptions.value = response.data.map((item) => ({
          label: `${item.customer_name} (${item.customer_num})`,
          value: item.customer_num,
        }));
      }
    } catch (error) {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "加载客户列表失败",
        life: 3000,
      });
    } finally {
      loading.value = false;
    }
  };

  return {
    customerOptions: labelFormat === "raw-data" ? (customerOptions as any) : customerOptions,
    loading,
    loadCustomerOptions,
  };
}

export function useCustomerOptionsWithId() {
  const toast = useToast();
  
  const customerOptions = ref<CustomerOptionWithId[]>([]);
  const loading = ref(false);

  const loadCustomerOptions = async () => {
    try {
      loading.value = true;
      const response = await getCustomersSimpleList();
      customerOptions.value = response.data.map((item) => ({
        label: `${item.customer_name} (${item.customer_num})`,
        value: item.customer_num,
        customer_id: item.id,
      }));
    } catch (error) {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "加载客户列表失败",
        life: 3000,
      });
    } finally {
      loading.value = false;
    }
  };

  return {
    customerOptions,
    loading,
    loadCustomerOptions,
  };
}